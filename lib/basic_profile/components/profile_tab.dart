import 'dart:io';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:aslaa/constant.dart';
import 'package:aslaa/models/user.dart';
import 'package:aslaa/providers/app_provider.dart';
import 'package:awesome_snackbar_content/awesome_snackbar_content.dart';

import '../../flutter_flow/flutter_flow_icon_button.dart';
import '../../flutter_flow/flutter_flow_language_selector.dart';
import '../../flutter_flow/flutter_flow_theme.dart';
import '../../flutter_flow/flutter_flow_util.dart';
import '../../flutter_flow/flutter_flow_widgets.dart';

import '../models/profile_form_model.dart';
import '../services/profile_service.dart';
import '../utils/image_utils.dart';
import 'custom_text_field.dart';

/// Widget for the Profile tab containing application settings and basic profile
class ProfileTab extends StatefulWidget {
  final User user;
  final AppProvider authProvider;
  final ProfileFormModel formModel;
  final Function(ProfileFormModel) onFormModelChanged;

  const ProfileTab({
    Key? key,
    required this.user,
    required this.authProvider,
    required this.formModel,
    required this.onFormModelChanged,
  }) : super(key: key);

  @override
  _ProfileTabState createState() => _ProfileTabState();
}

class _ProfileTabState extends State<ProfileTab> {
  @override
  void initState() {
    super.initState();
    // Populate form with user data
    widget.formModel.populateFromUser(
      fullName: widget.user.username,
      citizenId: widget.user.description,
      address: widget.user.address,
    );
  }

  Future<void> _pickImage() async {
    final File? image = await ImageUtils.pickImageFromGallery();
    if (image != null) {
      final updatedModel = widget.formModel.copyWith(selectedImage: image);
      widget.onFormModelChanged(updatedModel);
    }
  }

  Future<void> _saveProfile() async {
    if (!widget.formModel.isValid) {
      showAnimatedSnackbar(
        context,
        widget.formModel.validationErrors.join('\n'),
        "Validation Error",
        ContentType.failure,
      );
      return;
    }

    // Update loading state
    widget.onFormModelChanged(widget.formModel.copyWith(isLoading: true));

    final result = await ProfileService.saveProfile(
      authProvider: widget.authProvider,
      fullName: widget.formModel.fullNameController.text,
      address: widget.formModel.addressController.text,
      citizenId: widget.formModel.citizenController.text,
      licenseFile: widget.formModel.selectedImage,
    );

    // Update loading state
    widget.onFormModelChanged(widget.formModel.copyWith(isLoading: false));

    if (result.success) {
      showAnimatedSnackbar(
        context,
        result.message,
        "Success",
        ContentType.success,
      );
      widget.authProvider.reload();
    } else {
      showAnimatedSnackbar(
        context,
        result.message,
        "Error",
        ContentType.failure,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: MediaQuery.of(context).size.width,
      child: Padding(
        padding: EdgeInsetsDirectional.fromSTEB(0, 24, 0, 0),
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.max,
            children: [
              _buildApplicationSettings(),
              _buildBasicProfile(),
              _buildSaveButton(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildApplicationSettings() {
    return Column(
      children: [
        Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Padding(
              padding: EdgeInsetsDirectional.fromSTEB(24, 0, 0, 0),
              child: Text(
                FFLocalizations.of(context).getText('vripvise'),
                style: FlutterFlowTheme.of(context).bodyText1,
              ),
            ),
          ],
        ),
        _buildLanguageSelector(),
        _buildThemeToggle(),
      ],
    );
  }

  Widget _buildLanguageSelector() {
    return Padding(
      padding: EdgeInsetsDirectional.fromSTEB(30, 12, 24, 0),
      child: Row(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsetsDirectional.fromSTEB(0, 0, 12, 0),
            child: Text(
              FFLocalizations.of(context).getText('0s1u1rgc'),
              style: FlutterFlowTheme.of(context).bodyText1.override(
                fontFamily: 'Roboto',
                color: FlutterFlowTheme.of(context).secondaryText,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          FlutterFlowLanguageSelector(
            width: 200,
            backgroundColor: FlutterFlowTheme.of(context).secondaryBackground,
            borderColor: FlutterFlowTheme.of(context).secondaryBackground,
            dropdownColor: FlutterFlowTheme.of(context).secondaryBackground,
            dropdownIconColor: FlutterFlowTheme.of(context).primaryText,
            borderRadius: 8,
            textStyle: FlutterFlowTheme.of(context).bodyText2.override(
              fontFamily: 'Roboto',
              fontWeight: FontWeight.normal,
            ),
            hideFlags: true,
            flagSize: 24,
            flagTextGap: 8,
            currentLanguage: FFLocalizations.of(context).languageCode,
            languages: FFLocalizations.languages(),
            onChanged: (lang) => setAppLanguage(context, lang),
          ),
        ],
      ),
    );
  }

  Widget _buildThemeToggle() {
    return Padding(
      padding: EdgeInsetsDirectional.fromSTEB(30, 12, 24, 0),
      child: Row(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsetsDirectional.fromSTEB(0, 0, 12, 0),
            child: Text(
              FFLocalizations.of(context).getText('r31da99p'),
              style: FlutterFlowTheme.of(context).bodyText1.override(
                fontFamily: 'Roboto',
                color: FlutterFlowTheme.of(context).secondaryText,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          FlutterFlowIconButton(
            borderColor: Colors.transparent,
            borderRadius: 20,
            borderWidth: 1,
            buttonSize: 40,
            icon: FaIcon(
              Theme.of(context).brightness == Brightness.light
                  ? FontAwesomeIcons.moon
                  : FontAwesomeIcons.sun,
              color: FlutterFlowTheme.of(context).primaryText,
              size: 20,
            ),
            onPressed: () {
              final ThemeMode mode = Theme.of(context).brightness == Brightness.light
                  ? ThemeMode.dark
                  : ThemeMode.light;
              setDarkModeSetting(context, mode);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildBasicProfile() {
    return Column(
      children: [
        Padding(
          padding: EdgeInsetsDirectional.fromSTEB(0, 20, 0, 0),
          child: Row(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Padding(
                padding: EdgeInsetsDirectional.fromSTEB(24, 0, 0, 0),
                child: Text(
                  FFLocalizations.of(context).getText('oqpmjpr7'),
                  style: FlutterFlowTheme.of(context).bodyText1,
                ),
              ),
            ],
          ),
        ),
        _buildImagePicker(),
        _buildImageDescription(),
        _buildProfileFields(),
      ],
    );
  }

  Widget _buildImagePicker() {
    return Padding(
      padding: EdgeInsetsDirectional.fromSTEB(0, 12, 0, 0),
      child: GestureDetector(
        onTap: _pickImage,
        child: _getImageWidget(),
      ),
    );
  }

  Widget _getImageWidget() {
    if (widget.user.driverLicenseFile.isNotEmpty && widget.formModel.selectedImage == null) {
      return Image.network(
        '$API_HOST/${widget.user.driverLicenseFile}',
        width: 300,
        height: 160,
        fit: BoxFit.contain,
      );
    } else if (widget.formModel.selectedImage != null) {
      return Image.file(
        widget.formModel.selectedImage!,
        width: 300,
        height: 160,
        fit: BoxFit.contain,
      );
    } else {
      return Image.asset(
        'assets/images/driver-license.png',
        width: 300,
        height: 160,
        fit: BoxFit.contain,
      );
    }
  }

  Widget _buildImageDescription() {
    return Padding(
      padding: EdgeInsetsDirectional.fromSTEB(24, 24, 24, 0),
      child: Text(
        FFLocalizations.of(context).getText('ax1mce7n'),
        style: FlutterFlowTheme.of(context).bodyText1,
      ),
    );
  }

  Widget _buildProfileFields() {
    return Column(
      children: [
        CustomTextField(
          controller: widget.formModel.fullNameController,
          labelText: FFLocalizations.of(context).getText('olzchg1q'),
          hintText: FFLocalizations.of(context).getText('davxz5o5'),
        ),
        CustomTextField(
          controller: widget.formModel.citizenController,
          labelText: FFLocalizations.of(context).getText('akyocoli'),
          hintText: FFLocalizations.of(context).getText('w96lhppv'),
        ),
        CustomTextField(
          controller: widget.formModel.addressController,
          labelText: FFLocalizations.of(context).getText('otyc81f3'),
          hintText: FFLocalizations.of(context).getText('pkd1iehz'),
        ),
      ],
    );
  }

  Widget _buildSaveButton() {
    return Padding(
      padding: EdgeInsetsDirectional.fromSTEB(24, 24, 24, 24),
      child: FFButtonWidget(
        onPressed: widget.formModel.isLoading ? null : _saveProfile,
        text: widget.formModel.isLoading 
            ? "Saving..." 
            : FFLocalizations.of(context).getText('save'),
        options: FFButtonOptions(
          width: double.infinity,
          height: 50,
          color: FlutterFlowTheme.of(context).primaryColor,
          textStyle: FlutterFlowTheme.of(context).subtitle2.override(
            fontFamily: 'Roboto',
            color: Colors.white,
            fontWeight: FontWeight.w500,
          ),
          borderSide: BorderSide(
            color: Colors.transparent,
            width: 1,
          ),
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }
}
