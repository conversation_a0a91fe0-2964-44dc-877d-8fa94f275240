import 'package:flutter/material.dart';
import 'package:aslaa/models/user.dart';
import 'package:aslaa/models/device.dart';
import 'package:aslaa/providers/app_provider.dart';
import 'package:awesome_snackbar_content/awesome_snackbar_content.dart';

import '../../flutter_flow/flutter_flow_theme.dart';
import '../../flutter_flow/flutter_flow_util.dart';
import '../../flutter_flow/flutter_flow_widgets.dart';

import '../models/device_form_model.dart';
import '../models/payment_form_model.dart';
import '../services/payment_service.dart';
import 'custom_text_field.dart';
import 'payment_dialog.dart';

/// Widget for the Devices List tab showing user devices and payment options
class DevicesListTab extends StatefulWidget {
  final User user;
  final AppProvider authProvider;
  final DeviceFormModel deviceFormModel;
  final PaymentFormModel paymentFormModel;
  final Function(DeviceFormModel) onDeviceFormChanged;
  final Function(PaymentFormModel) onPaymentFormChanged;
  final Function(Device) onEditDevice;

  const DevicesListTab({
    Key? key,
    required this.user,
    required this.authProvider,
    required this.deviceFormModel,
    required this.paymentFormModel,
    required this.onDeviceFormChanged,
    required this.onPaymentFormChanged,
    required this.onEditDevice,
  }) : super(key: key);

  @override
  _DevicesListTabState createState() => _DevicesListTabState();
}

class _DevicesListTabState extends State<DevicesListTab> {
  Future<void> _processDeposit() async {
    final amount = widget.paymentFormModel.amount;
    if (amount == null || amount <= 1000) {
      showAnimatedSnackbar(
        context,
        "Deposit amount must be greater than 1000",
        "Validation Error",
        ContentType.failure,
      );
      return;
    }

    // Update loading state
    widget.onPaymentFormChanged(widget.paymentFormModel.copyWith(isLoading: true));

    final result = await PaymentService.processDepositRequest(
      provider: widget.authProvider,
      amount: amount,
    );

    // Update loading state
    widget.onPaymentFormChanged(widget.paymentFormModel.copyWith(isLoading: false));

    if (result.success && result.data != null) {
      // Show payment dialog
      showDialog(
        context: context,
        builder: (context) => PaymentDialog(
          qrcode: result.data['qrcode'],
          urls: result.data['urls'],
          onClose: () {
            widget.authProvider.reload();
            Navigator.pop(context);
          },
        ),
      );
    } else {
      showAnimatedSnackbar(
        context,
        result.message,
        "Error",
        ContentType.failure,
      );
    }
  }

  Future<void> _processWithdraw() async {
    final amount = widget.paymentFormModel.amount;
    final userBalance = widget.user.balance;
    
    if (amount == null || amount <= 1000) {
      showAnimatedSnackbar(
        context,
        "Withdraw amount must be greater than 1000",
        "Validation Error",
        ContentType.failure,
      );
      return;
    }

    if (amount > userBalance) {
      showAnimatedSnackbar(
        context,
        "Withdraw amount cannot be greater than user balance",
        "Validation Error",
        ContentType.failure,
      );
      return;
    }

    // Update loading state
    widget.onPaymentFormChanged(widget.paymentFormModel.copyWith(isLoading: true));

    final result = await PaymentService.processWithdrawRequest(
      provider: widget.authProvider,
      amount: amount,
    );

    // Update loading state
    widget.onPaymentFormChanged(widget.paymentFormModel.copyWith(isLoading: false));

    if (result.success) {
      showAnimatedSnackbar(
        context,
        result.message,
        "Success",
        ContentType.success,
      );
      widget.authProvider.reload();
      widget.paymentFormModel.clear();
    } else {
      showAnimatedSnackbar(
        context,
        result.message,
        "Error",
        ContentType.failure,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: MediaQuery.of(context).size.width,
      child: Padding(
        padding: EdgeInsetsDirectional.fromSTEB(0, 24, 0, 0),
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.max,
            children: [
              _buildBalanceSection(),
              _buildPaymentSection(),
              _buildDevicesSection(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBalanceSection() {
    return Container(
      width: double.infinity,
      margin: EdgeInsetsDirectional.fromSTEB(24, 0, 24, 24),
      padding: EdgeInsetsDirectional.fromSTEB(20, 20, 20, 20),
      decoration: BoxDecoration(
        color: FlutterFlowTheme.of(context).secondaryBackground,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            blurRadius: 4,
            color: Color(0x33000000),
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            FFLocalizations.of(context).getText('current_balance') ?? 'Current Balance',
            style: FlutterFlowTheme.of(context).bodyText2,
          ),
          SizedBox(height: 8),
          Text(
            '₮${getFormatedNumber(widget.user.balance)}',
            style: FlutterFlowTheme.of(context).title1.override(
              fontFamily: 'Roboto',
              color: FlutterFlowTheme.of(context).primaryColor,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsetsDirectional.fromSTEB(24, 0, 24, 12),
          child: Text(
            FFLocalizations.of(context).getText('payment_operations') ?? 'Payment Operations',
            style: FlutterFlowTheme.of(context).title3,
          ),
        ),
        CustomTextField(
          controller: widget.paymentFormModel.amountController,
          labelText: FFLocalizations.of(context).getText('amount') ?? 'Amount',
          hintText: FFLocalizations.of(context).getText('enter_amount') ?? 'Enter amount',
          keyboardType: TextInputType.number,
        ),
        Padding(
          padding: EdgeInsetsDirectional.fromSTEB(24, 24, 24, 0),
          child: Row(
            children: [
              Expanded(
                child: FFButtonWidget(
                  onPressed: widget.paymentFormModel.isLoading ? null : _processDeposit,
                  text: widget.paymentFormModel.isLoading 
                      ? "Processing..." 
                      : FFLocalizations.of(context).getText('deposit') ?? 'Deposit',
                  options: FFButtonOptions(
                    height: 50,
                    color: FlutterFlowTheme.of(context).primaryColor,
                    textStyle: FlutterFlowTheme.of(context).subtitle2.override(
                      fontFamily: 'Roboto',
                      color: Colors.white,
                      fontWeight: FontWeight.w500,
                    ),
                    borderSide: BorderSide(
                      color: Colors.transparent,
                      width: 1,
                    ),
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
              SizedBox(width: 12),
              Expanded(
                child: FFButtonWidget(
                  onPressed: widget.paymentFormModel.isLoading ? null : _processWithdraw,
                  text: widget.paymentFormModel.isLoading 
                      ? "Processing..." 
                      : FFLocalizations.of(context).getText('withdraw') ?? 'Withdraw',
                  options: FFButtonOptions(
                    height: 50,
                    color: FlutterFlowTheme.of(context).secondaryColor,
                    textStyle: FlutterFlowTheme.of(context).subtitle2.override(
                      fontFamily: 'Roboto',
                      color: FlutterFlowTheme.of(context).primaryText,
                      fontWeight: FontWeight.w500,
                    ),
                    borderSide: BorderSide(
                      color: Colors.transparent,
                      width: 1,
                    ),
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildDevicesSection() {
    final devices = widget.user.devices ?? [];
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsetsDirectional.fromSTEB(24, 32, 24, 12),
          child: Text(
            FFLocalizations.of(context).getText('my_devices') ?? 'My Devices',
            style: FlutterFlowTheme.of(context).title3,
          ),
        ),
        if (devices.isEmpty)
          Padding(
            padding: EdgeInsetsDirectional.fromSTEB(24, 12, 24, 24),
            child: Text(
              FFLocalizations.of(context).getText('no_devices') ?? 'No devices found',
              style: FlutterFlowTheme.of(context).bodyText2,
            ),
          )
        else
          ...devices.map((device) => _buildDeviceCard(device)).toList(),
      ],
    );
  }

  Widget _buildDeviceCard(Device device) {
    return Container(
      width: double.infinity,
      margin: EdgeInsetsDirectional.fromSTEB(24, 0, 24, 12),
      padding: EdgeInsetsDirectional.fromSTEB(16, 16, 16, 16),
      decoration: BoxDecoration(
        color: FlutterFlowTheme.of(context).secondaryBackground,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: device.isDefault 
              ? FlutterFlowTheme.of(context).primaryColor 
              : FlutterFlowTheme.of(context).lineColor,
          width: device.isDefault ? 2 : 1,
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  device.deviceName.isNotEmpty ? device.deviceName : device.deviceNumber,
                  style: FlutterFlowTheme.of(context).bodyText1.override(
                    fontFamily: 'Roboto',
                    fontWeight: FontWeight.w600,
                  ),
                ),
                SizedBox(height: 4),
                Text(
                  '${device.deviceNumber} • ${device.type.toUpperCase()} • ${device.uix}',
                  style: FlutterFlowTheme.of(context).bodyText2,
                ),
                if (device.isDefault)
                  Padding(
                    padding: EdgeInsetsDirectional.fromSTEB(0, 4, 0, 0),
                    child: Text(
                      FFLocalizations.of(context).getText('default_device') ?? 'Default Device',
                      style: FlutterFlowTheme.of(context).bodyText2.override(
                        fontFamily: 'Roboto',
                        color: FlutterFlowTheme.of(context).primaryColor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
              ],
            ),
          ),
          IconButton(
            onPressed: () => widget.onEditDevice(device),
            icon: Icon(
              Icons.edit,
              color: FlutterFlowTheme.of(context).secondaryText,
            ),
          ),
        ],
      ),
    );
  }
}
