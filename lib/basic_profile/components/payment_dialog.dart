import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../flutter_flow/flutter_flow_theme.dart';
import '../../flutter_flow/flutter_flow_util.dart';
import '../../flutter_flow/flutter_flow_widgets.dart';
import '../services/payment_service.dart';

/// Dialog widget for displaying payment QR code and bank options
class PaymentDialog extends StatelessWidget {
  final String qrcode;
  final List<dynamic>? urls;
  final VoidCallback onClose;

  const PaymentDialog({
    Key? key,
    required this.qrcode,
    required this.urls,
    required this.onClose,
  }) : super(key: key);

  Future<void> _launchUrl(String url) async {
    try {
      if (!await launchUrl(Uri.parse(url))) {
        throw Exception('Could not launch url: $url');
      }
    } catch (err) {
      print('Error launching URL: $err');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog.fullscreen(
      child: SingleChildScrollView(
        child: Column(
          children: [
            _buildQRCodeSection(context),
            _buildBankOptions(context),
            _buildCloseButton(context),
          ],
        ),
      ),
    );
  }

  Widget _buildQRCodeSection(BuildContext context) {
    return Padding(
      padding: EdgeInsetsDirectional.fromSTEB(24, 24, 0, 24),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Image.memory(
            PaymentService.convertBase64Image(qrcode),
            gaplessPlayback: true,
            width: 180,
            height: 180,
          ),
        ],
      ),
    );
  }

  Widget _buildBankOptions(BuildContext context) {
    if (urls == null || urls!.isEmpty) {
      return SizedBox.shrink();
    }

    return Padding(
      padding: EdgeInsetsDirectional.fromSTEB(24, 24, 24, 0),
      child: Column(
        children: urls!
            .map((bankOption) => _buildBankOptionItem(context, bankOption))
            .toList(),
      ),
    );
  }

  Widget _buildBankOptionItem(BuildContext context, dynamic bankOption) {
    return GestureDetector(
      onTap: () async {
        await _launchUrl(bankOption['link']);
      },
      child: Container(
        width: double.infinity,
        padding: EdgeInsetsDirectional.fromSTEB(16, 12, 16, 12),
        margin: EdgeInsetsDirectional.fromSTEB(0, 0, 0, 8),
        decoration: BoxDecoration(
          color: FlutterFlowTheme.of(context).secondaryBackground,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: FlutterFlowTheme.of(context).lineColor,
            width: 1,
          ),
        ),
        child: Row(
          children: [
            Image.network(
              bankOption['logo'],
              width: 24,
              height: 24,
              errorBuilder: (context, error, stackTrace) {
                return Icon(
                  Icons.account_balance,
                  size: 24,
                  color: FlutterFlowTheme.of(context).secondaryText,
                );
              },
            ),
            Padding(
              padding: EdgeInsetsDirectional.fromSTEB(12, 0, 0, 0),
              child: Text(
                bankOption['name'] ?? 'Bank',
                style: FlutterFlowTheme.of(context).bodyText1.override(
                  fontFamily: 'Roboto',
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            Spacer(),
            Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: FlutterFlowTheme.of(context).secondaryText,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCloseButton(BuildContext context) {
    return Padding(
      padding: EdgeInsetsDirectional.fromSTEB(24, 24, 24, 24),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          FFButtonWidget(
            onPressed: onClose,
            text: FFLocalizations.of(context).getText('b3bahb30') ?? 'Close',
            options: FFButtonOptions(
              width: 130,
              height: 40,
              color: FlutterFlowTheme.of(context).secondaryColor,
              textStyle: FlutterFlowTheme.of(context).subtitle2.override(
                fontFamily: 'Roboto',
                color: FlutterFlowTheme.of(context).primaryText,
                fontWeight: FontWeight.w500,
              ),
              borderSide: BorderSide(
                color: Colors.transparent,
                width: 1,
              ),
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        ],
      ),
    );
  }
}
