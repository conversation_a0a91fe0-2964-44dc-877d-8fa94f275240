import 'package:flutter/material.dart';
import 'package:aslaa/models/user.dart';
import 'package:aslaa/providers/app_provider.dart';
import 'package:awesome_snackbar_content/awesome_snackbar_content.dart';

import '../../flutter_flow/flutter_flow_theme.dart';
import '../../flutter_flow/flutter_flow_util.dart';
import '../../flutter_flow/flutter_flow_widgets.dart';

import '../models/security_form_model.dart';
import '../models/bank_form_model.dart';
import '../services/security_service.dart';
import '../services/bank_service.dart';
import 'custom_text_field.dart';

/// Widget for the Security tab containing password and bank information
class SecurityTab extends StatefulWidget {
  final User user;
  final AppProvider authProvider;
  final SecurityFormModel securityFormModel;
  final BankFormModel bankFormModel;
  final Function(SecurityFormModel) onSecurityFormChanged;
  final Function(BankFormModel) onBankFormChanged;

  const SecurityTab({
    Key? key,
    required this.user,
    required this.authProvider,
    required this.securityFormModel,
    required this.bankFormModel,
    required this.onSecurityFormChanged,
    required this.onBankFormChanged,
  }) : super(key: key);

  @override
  _SecurityTabState createState() => _SecurityTabState();
}

class _SecurityTabState extends State<SecurityTab> {
  @override
  void initState() {
    super.initState();
    // Populate bank form with user data
    widget.bankFormModel.populateFromBankData(
      bankAccount: widget.user.wallet.bankAccount,
      bankName: widget.user.wallet.bankName,
    );
  }

  Future<void> _updatePassword() async {
    final validationErrors = widget.securityFormModel.getValidationErrors(widget.user.password);
    
    if (validationErrors.isNotEmpty) {
      showAnimatedSnackbar(
        context,
        validationErrors.join('\n'),
        "Validation Error",
        ContentType.failure,
      );
      return;
    }

    // Update loading state
    widget.onSecurityFormChanged(widget.securityFormModel.copyWith(isLoading: true));

    final result = await SecurityService.updatePassword(
      authProvider: widget.authProvider,
      oldPassword: widget.securityFormModel.oldPasswordController.text,
      newPassword: widget.securityFormModel.passwordController.text,
      confirmPassword: widget.securityFormModel.confirmPasswordController.text,
    );

    // Update loading state
    widget.onSecurityFormChanged(widget.securityFormModel.copyWith(isLoading: false));

    if (result.success) {
      showAnimatedSnackbar(
        context,
        result.message,
        "Success",
        ContentType.success,
      );
      
      // Update user's password in provider
      if (result.data != null && result.data['newPassword'] != null) {
        widget.authProvider.updatePassword(password: result.data['newPassword']);
      }
      
      // Clear form
      widget.securityFormModel.clear();
      widget.onSecurityFormChanged(widget.securityFormModel.copyWith(clearError: true));
    } else {
      showAnimatedSnackbar(
        context,
        result.message,
        "Error",
        ContentType.failure,
      );
    }
  }

  Future<void> _saveBankInformation() async {
    if (!widget.bankFormModel.isValid) {
      showAnimatedSnackbar(
        context,
        widget.bankFormModel.validationErrors.join('\n'),
        "Validation Error",
        ContentType.failure,
      );
      return;
    }

    // Update loading state
    widget.onBankFormChanged(widget.bankFormModel.copyWith(isLoading: true));

    final result = await BankService.saveBankInformation(
      authProvider: widget.authProvider,
      bankAccount: widget.bankFormModel.bankAccountController.text,
      bankName: widget.bankFormModel.bankNameController.text,
    );

    // Update loading state
    widget.onBankFormChanged(widget.bankFormModel.copyWith(isLoading: false));

    if (result.success) {
      showAnimatedSnackbar(
        context,
        result.message,
        "Success",
        ContentType.success,
      );
      
      // Update user's wallet in provider
      if (result.data != null) {
        widget.authProvider.updateWallet(
          bankName: result.data['bankName'],
          bankAccount: result.data['bankAccount'],
        );
      }
    } else {
      showAnimatedSnackbar(
        context,
        result.message,
        "Error",
        ContentType.failure,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: MediaQuery.of(context).size.width,
      child: Padding(
        padding: EdgeInsetsDirectional.fromSTEB(0, 24, 0, 0),
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.max,
            children: [
              _buildPasswordSection(),
              _buildBankSection(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPasswordSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsetsDirectional.fromSTEB(24, 0, 24, 12),
          child: Text(
            FFLocalizations.of(context).getText('change_password') ?? 'Change Password',
            style: FlutterFlowTheme.of(context).title3,
          ),
        ),
        CustomPasswordField(
          controller: widget.securityFormModel.oldPasswordController,
          labelText: FFLocalizations.of(context).getText('current_password') ?? 'Current Password',
          hintText: FFLocalizations.of(context).getText('enter_current_password') ?? 'Enter current password',
          isVisible: widget.securityFormModel.oldPasswordVisibility,
          onVisibilityToggle: () {
            widget.securityFormModel.toggleOldPasswordVisibility();
            widget.onSecurityFormChanged(widget.securityFormModel.copyWith(
              oldPasswordVisibility: widget.securityFormModel.oldPasswordVisibility,
            ));
          },
        ),
        CustomPasswordField(
          controller: widget.securityFormModel.passwordController,
          labelText: FFLocalizations.of(context).getText('new_password') ?? 'New Password',
          hintText: FFLocalizations.of(context).getText('enter_new_password') ?? 'Enter new password',
          isVisible: widget.securityFormModel.passwordVisibility,
          onVisibilityToggle: () {
            widget.securityFormModel.togglePasswordVisibility();
            widget.onSecurityFormChanged(widget.securityFormModel.copyWith(
              passwordVisibility: widget.securityFormModel.passwordVisibility,
            ));
          },
        ),
        CustomPasswordField(
          controller: widget.securityFormModel.confirmPasswordController,
          labelText: FFLocalizations.of(context).getText('confirm_password') ?? 'Confirm Password',
          hintText: FFLocalizations.of(context).getText('confirm_new_password') ?? 'Confirm new password',
          isVisible: widget.securityFormModel.confirmPasswordVisibility,
          onVisibilityToggle: () {
            widget.securityFormModel.toggleConfirmPasswordVisibility();
            widget.onSecurityFormChanged(widget.securityFormModel.copyWith(
              confirmPasswordVisibility: widget.securityFormModel.confirmPasswordVisibility,
            ));
          },
        ),
        Padding(
          padding: EdgeInsetsDirectional.fromSTEB(24, 24, 24, 0),
          child: FFButtonWidget(
            onPressed: widget.securityFormModel.isLoading ? null : _updatePassword,
            text: widget.securityFormModel.isLoading 
                ? "Updating..." 
                : FFLocalizations.of(context).getText('update_password') ?? 'Update Password',
            options: FFButtonOptions(
              width: double.infinity,
              height: 50,
              color: FlutterFlowTheme.of(context).primaryColor,
              textStyle: FlutterFlowTheme.of(context).subtitle2.override(
                fontFamily: 'Roboto',
                color: Colors.white,
                fontWeight: FontWeight.w500,
              ),
              borderSide: BorderSide(
                color: Colors.transparent,
                width: 1,
              ),
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildBankSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsetsDirectional.fromSTEB(24, 32, 24, 12),
          child: Text(
            FFLocalizations.of(context).getText('bank_information') ?? 'Bank Information',
            style: FlutterFlowTheme.of(context).title3,
          ),
        ),
        CustomTextField(
          controller: widget.bankFormModel.bankNameController,
          labelText: FFLocalizations.of(context).getText('bank_name') ?? 'Bank Name',
          hintText: FFLocalizations.of(context).getText('enter_bank_name') ?? 'Enter bank name',
        ),
        CustomTextField(
          controller: widget.bankFormModel.bankAccountController,
          labelText: FFLocalizations.of(context).getText('bank_account') ?? 'Bank Account',
          hintText: FFLocalizations.of(context).getText('enter_bank_account') ?? 'Enter bank account number',
          keyboardType: TextInputType.number,
        ),
        Padding(
          padding: EdgeInsetsDirectional.fromSTEB(24, 24, 24, 24),
          child: FFButtonWidget(
            onPressed: widget.bankFormModel.isLoading ? null : _saveBankInformation,
            text: widget.bankFormModel.isLoading 
                ? "Saving..." 
                : FFLocalizations.of(context).getText('save_bank_info') ?? 'Save Bank Information',
            options: FFButtonOptions(
              width: double.infinity,
              height: 50,
              color: FlutterFlowTheme.of(context).secondaryColor,
              textStyle: FlutterFlowTheme.of(context).subtitle2.override(
                fontFamily: 'Roboto',
                color: FlutterFlowTheme.of(context).primaryText,
                fontWeight: FontWeight.w500,
              ),
              borderSide: BorderSide(
                color: Colors.transparent,
                width: 1,
              ),
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        ),
      ],
    );
  }
}
