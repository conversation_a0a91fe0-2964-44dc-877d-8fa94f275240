import 'package:flutter/material.dart';
import 'package:aslaa/models/user.dart';
import 'package:aslaa/providers/app_provider.dart';
import 'package:awesome_snackbar_content/awesome_snackbar_content.dart';

import '../../flutter_flow/flutter_flow_drop_down.dart';
import '../../flutter_flow/flutter_flow_radio_button.dart';
import '../../flutter_flow/flutter_flow_theme.dart';
import '../../flutter_flow/flutter_flow_util.dart';
import '../../flutter_flow/flutter_flow_widgets.dart';

import '../models/device_form_model.dart';
import '../services/device_service.dart';
import 'custom_text_field.dart';

/// Widget for the Device Configuration tab
class DeviceTab extends StatefulWidget {
  final User user;
  final AppProvider authProvider;
  final DeviceFormModel formModel;
  final Function(DeviceFormModel) onFormModelChanged;

  const DeviceTab({
    Key? key,
    required this.user,
    required this.authProvider,
    required this.formModel,
    required this.onFormModelChanged,
  }) : super(key: key);

  @override
  _DeviceTabState createState() => _DeviceTabState();
}

class _DeviceTabState extends State<DeviceTab> {
  final List<String> uixOptions = [
    'CarV1.2',
    'CarV1.3',
    'CarV2.0',
    'MotorV1.0',
    'MotorV1.1',
    'MotorV1.2',
  ];

  Future<void> _saveDevice() async {
    if (!widget.formModel.isValid) {
      showAnimatedSnackbar(
        context,
        widget.formModel.validationErrors.join('\n'),
        "Validation Error",
        ContentType.failure,
      );
      return;
    }

    // Update loading state
    widget.onFormModelChanged(widget.formModel.copyWith(isLoading: true));

    final result = await DeviceService.saveDevice(
      authProvider: widget.authProvider,
      deviceName: widget.formModel.deviceNameController.text,
      deviceNumber: widget.formModel.deviceNumberController.text,
      uix: widget.formModel.dropDownValue,
      isDefault: widget.formModel.switchValue,
      type: widget.formModel.radioButtonValue,
      mode: widget.formModel.mode,
      selectedDevice: widget.formModel.selectedDevice,
    );

    // Update loading state
    widget.onFormModelChanged(widget.formModel.copyWith(isLoading: false));

    if (result.success) {
      showAnimatedSnackbar(
        context,
        result.message,
        "Success",
        ContentType.success,
      );
      widget.authProvider.reload();
      // Reset form for new device
      widget.onFormModelChanged(widget.formModel.copyWith(clearError: true));
      widget.formModel.resetForNewDevice();
    } else {
      showAnimatedSnackbar(
        context,
        result.message,
        "Error",
        ContentType.failure,
      );
    }
  }

  Future<void> _deleteDevice() async {
    if (widget.formModel.selectedDevice == null) return;

    // Show confirmation dialog
    final bool? confirmed = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('Confirm Delete'),
          content: Text('Are you sure you want to delete this device?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: Text('Cancel'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: Text('Delete'),
            ),
          ],
        );
      },
    );

    if (confirmed != true) return;

    // Update loading state
    widget.onFormModelChanged(widget.formModel.copyWith(isLoading: true));

    final result = await DeviceService.deleteDevice(
      authProvider: widget.authProvider,
      device: widget.formModel.selectedDevice!,
    );

    // Update loading state
    widget.onFormModelChanged(widget.formModel.copyWith(isLoading: false));

    if (result.success) {
      showAnimatedSnackbar(
        context,
        result.message,
        "Success",
        ContentType.success,
      );
      widget.authProvider.reload();
      // Reset form
      widget.formModel.resetForNewDevice();
      widget.onFormModelChanged(widget.formModel.copyWith(clearError: true));
    } else {
      showAnimatedSnackbar(
        context,
        result.message,
        "Error",
        ContentType.failure,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: MediaQuery.of(context).size.width,
      child: Padding(
        padding: EdgeInsetsDirectional.fromSTEB(0, 24, 0, 0),
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.max,
            children: [
              _buildDeviceForm(),
              _buildActionButtons(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDeviceForm() {
    return Column(
      children: [
        CustomTextField(
          controller: widget.formModel.deviceNameController,
          labelText: FFLocalizations.of(context).getText('device_name') ?? 'Device Name',
          hintText: FFLocalizations.of(context).getText('enter_device_name') ?? 'Enter device name',
        ),
        CustomTextField(
          controller: widget.formModel.deviceNumberController,
          labelText: FFLocalizations.of(context).getText('device_number') ?? 'Device Number',
          hintText: FFLocalizations.of(context).getText('enter_device_number') ?? 'Enter device number',
        ),
        _buildUIXDropdown(),
        _buildDeviceTypeRadio(),
        _buildDefaultSwitch(),
      ],
    );
  }

  Widget _buildUIXDropdown() {
    return Padding(
      padding: EdgeInsetsDirectional.fromSTEB(24, 12, 24, 0),
      child: FlutterFlowDropDown<String>(
        value: widget.formModel.dropDownValue,
        hint: Text(
          FFLocalizations.of(context).getText('select_uix') ?? 'Select UIX',
          style: FlutterFlowTheme.of(context).bodyText2,
        ),
        options: uixOptions,
        onChanged: (val) {
          widget.onFormModelChanged(
            widget.formModel.copyWith(dropDownValue: val),
          );
        },
        width: double.infinity,
        height: 50,
        textStyle: FlutterFlowTheme.of(context).bodyText1,
        hintTextStyle: FlutterFlowTheme.of(context).bodyText2,
        fillColor: FlutterFlowTheme.of(context).secondaryBackground,
        elevation: 2,
        borderColor: Colors.transparent,
        borderWidth: 0,
        borderRadius: 8,
        margin: EdgeInsetsDirectional.fromSTEB(12, 4, 12, 4),
        hidesUnderline: true,
      ),
    );
  }

  Widget _buildDeviceTypeRadio() {
    return Padding(
      padding: EdgeInsetsDirectional.fromSTEB(24, 12, 24, 0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            FFLocalizations.of(context).getText('device_type') ?? 'Device Type',
            style: FlutterFlowTheme.of(context).bodyText1,
          ),
          FlutterFlowRadioButton(
            options: ['4G', '5G', 'WiFi'],
            onChanged: (val) {
              widget.onFormModelChanged(
                widget.formModel.copyWith(radioButtonValue: val),
              );
            },
            optionHeight: 25,
            textStyle: FlutterFlowTheme.of(context).bodyText1,
            selectedTextStyle: FlutterFlowTheme.of(context).bodyText1,
            buttonPosition: RadioButtonPosition.left,
            direction: Axis.horizontal,
            radioButtonColor: FlutterFlowTheme.of(context).primaryColor,
            inactiveRadioButtonColor: FlutterFlowTheme.of(context).secondaryText,
            toggleable: false,
            horizontalAlignment: WrapAlignment.start,
            verticalAlignment: WrapCrossAlignment.start,
          ),
        ],
      ),
    );
  }

  Widget _buildDefaultSwitch() {
    return Padding(
      padding: EdgeInsetsDirectional.fromSTEB(24, 12, 24, 0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            FFLocalizations.of(context).getText('set_as_default') ?? 'Set as Default',
            style: FlutterFlowTheme.of(context).bodyText1,
          ),
          Switch(
            value: widget.formModel.switchValue,
            onChanged: (val) {
              widget.onFormModelChanged(
                widget.formModel.copyWith(switchValue: val),
              );
            },
            activeColor: FlutterFlowTheme.of(context).primaryColor,
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Padding(
      padding: EdgeInsetsDirectional.fromSTEB(24, 24, 24, 24),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          Expanded(
            child: FFButtonWidget(
              onPressed: widget.formModel.isLoading ? null : _saveDevice,
              text: widget.formModel.isLoading 
                  ? "Saving..." 
                  : (widget.formModel.mode == 'edit' ? 'Update Device' : 'Save Device'),
              options: FFButtonOptions(
                height: 50,
                color: FlutterFlowTheme.of(context).primaryColor,
                textStyle: FlutterFlowTheme.of(context).subtitle2.override(
                  fontFamily: 'Roboto',
                  color: Colors.white,
                  fontWeight: FontWeight.w500,
                ),
                borderSide: BorderSide(
                  color: Colors.transparent,
                  width: 1,
                ),
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
          if (widget.formModel.mode == 'edit') ...[
            SizedBox(width: 12),
            Expanded(
              child: FFButtonWidget(
                onPressed: widget.formModel.isLoading ? null : _deleteDevice,
                text: "Delete Device",
                options: FFButtonOptions(
                  height: 50,
                  color: Colors.red,
                  textStyle: FlutterFlowTheme.of(context).subtitle2.override(
                    fontFamily: 'Roboto',
                    color: Colors.white,
                    fontWeight: FontWeight.w500,
                  ),
                  borderSide: BorderSide(
                    color: Colors.transparent,
                    width: 1,
                  ),
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }
}
