import 'package:flutter/material.dart';
import '../../flutter_flow/flutter_flow_theme.dart';

/// Widget for creating consistent form sections with titles
class FormSection extends StatelessWidget {
  final String title;
  final List<Widget> children;
  final EdgeInsetsGeometry? padding;

  const FormSection({
    Key? key,
    required this.title,
    required this.children,
    this.padding,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: padding ?? EdgeInsetsDirectional.fromSTEB(24, 20, 24, 12),
          child: Text(
            title,
            style: FlutterFlowTheme.of(context).title3,
          ),
        ),
        ...children,
      ],
    );
  }
}

/// Widget for creating form rows with labels and controls
class FormRow extends StatelessWidget {
  final String label;
  final Widget control;
  final EdgeInsetsGeometry? padding;

  const FormRow({
    Key? key,
    required this.label,
    required this.control,
    this.padding,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: padding ?? EdgeInsetsDirectional.fromSTEB(24, 12, 24, 0),
      child: Row(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: FlutterFlowTheme.of(context).bodyText1.override(
              fontFamily: 'Roboto',
              color: FlutterFlowTheme.of(context).secondaryText,
              fontWeight: FontWeight.w500,
            ),
          ),
          control,
        ],
      ),
    );
  }
}

/// Widget for creating loading buttons with consistent styling
class LoadingButton extends StatelessWidget {
  final bool isLoading;
  final String text;
  final String loadingText;
  final VoidCallback? onPressed;
  final Color? color;
  final Color? textColor;
  final double? width;
  final double height;

  const LoadingButton({
    Key? key,
    required this.isLoading,
    required this.text,
    this.loadingText = "Loading...",
    this.onPressed,
    this.color,
    this.textColor,
    this.width,
    this.height = 50,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: width,
      height: height,
      child: ElevatedButton(
        onPressed: isLoading ? null : onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: color ?? FlutterFlowTheme.of(context).primaryColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        child: isLoading
            ? Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        textColor ?? Colors.white,
                      ),
                    ),
                  ),
                  SizedBox(width: 8),
                  Text(
                    loadingText,
                    style: TextStyle(
                      color: textColor ?? Colors.white,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              )
            : Text(
                text,
                style: TextStyle(
                  color: textColor ?? Colors.white,
                  fontWeight: FontWeight.w500,
                ),
              ),
      ),
    );
  }
}

/// Widget for displaying validation errors
class ValidationErrorDisplay extends StatelessWidget {
  final List<String> errors;

  const ValidationErrorDisplay({
    Key? key,
    required this.errors,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (errors.isEmpty) return SizedBox.shrink();

    return Container(
      width: double.infinity,
      margin: EdgeInsetsDirectional.fromSTEB(24, 12, 24, 0),
      padding: EdgeInsetsDirectional.fromSTEB(16, 12, 16, 12),
      decoration: BoxDecoration(
        color: Colors.red.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Colors.red.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.error_outline,
                color: Colors.red,
                size: 20,
              ),
              SizedBox(width: 8),
              Text(
                'Validation Errors',
                style: FlutterFlowTheme.of(context).bodyText1.override(
                  fontFamily: 'Roboto',
                  color: Colors.red,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          SizedBox(height: 8),
          ...errors.map((error) => Padding(
            padding: EdgeInsetsDirectional.fromSTEB(0, 2, 0, 2),
            child: Text(
              '• $error',
              style: FlutterFlowTheme.of(context).bodyText2.override(
                fontFamily: 'Roboto',
                color: Colors.red.shade700,
              ),
            ),
          )).toList(),
        ],
      ),
    );
  }
}
