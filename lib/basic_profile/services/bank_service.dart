import 'dart:convert';
import 'package:aslaa/constant.dart';
import 'package:aslaa/providers/app_provider.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';

/// Service class responsible for bank-related API operations
class BankService {
  /// Saves bank account information
  static Future<BankServiceResult> saveBankInformation({
    required AppProvider authProvider,
    required String bankAccount,
    required String bankName,
  }) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String token = prefs.getString('token') ?? authProvider.user!.token;
      
      Map<String, String> headers = {
        'Content-Type': 'application/json; charset=UTF-8',
        'Authorization': 'Bearer $token',
      };
      
      Map<String, String> body = {
        "bankAccount": bankAccount,
        "bankName": bankName,
      };
      
      var response = await http.post(
        Uri.parse('$API_HOST/api/auth/set-bank'),
        headers: headers,
        body: jsonEncode(body),
      );
      
      if (response.statusCode == 200) {
        return BankServiceResult(
          success: true,
          message: "Saved Bank Account Success",
          data: {
            'bankAccount': bankAccount,
            'bankName': bankName,
          },
        );
      } else {
        return BankServiceResult(
          success: false,
          message: "Internal Server Error - ${response.statusCode}",
        );
      }
    } catch (err) {
      return BankServiceResult(
        success: false,
        message: "Please check your network connection",
      );
    }
  }

  /// Validates bank information
  static BankValidationResult validateBankData({
    required String bankAccount,
    required String bankName,
  }) {
    List<String> errors = [];

    if (bankAccount.trim().isEmpty) {
      errors.add("Bank account number is required");
    }

    if (bankName.trim().isEmpty) {
      errors.add("Bank name is required");
    }

    // Additional validation for bank account format
    if (bankAccount.trim().isNotEmpty && bankAccount.trim().length < 8) {
      errors.add("Bank account number must be at least 8 characters");
    }

    return BankValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
    );
  }
}

/// Result class for bank service operations
class BankServiceResult {
  final bool success;
  final String message;
  final dynamic data;

  BankServiceResult({
    required this.success,
    required this.message,
    this.data,
  });
}

/// Result class for bank validation
class BankValidationResult {
  final bool isValid;
  final List<String> errors;

  BankValidationResult({
    required this.isValid,
    required this.errors,
  });
}
