import 'dart:convert';
import 'dart:io';
import 'package:aslaa/constant.dart';
import 'package:aslaa/models/user.dart';
import 'package:aslaa/providers/app_provider.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';

/// Service class responsible for profile-related API operations
class ProfileService {
  /// Saves user profile information including driver license file
  static Future<ProfileServiceResult> saveProfile({
    required AppProvider authProvider,
    required String fullName,
    required String address,
    required String citizenId,
    required File? licenseFile,
  }) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String token = prefs.getString('token') ?? authProvider.user!.token;
      
      if (licenseFile == null) {
        return ProfileServiceResult(
          success: false,
          message: "Please select your license file",
        );
      }

      Map<String, String> headers = {
        'Content-Type': 'multipart/form-data',
        'Authorization': 'Bearer $token',
      };
      
      Map<String, String> body = {
        "username": fullName,
        "address": address,
        "description": citizenId,
      };
      
      var request = http.MultipartRequest(
        'POST', 
        Uri.parse('$API_HOST/api/device/set-driver-profile')
      )
        ..fields.addAll(body)
        ..headers.addAll(headers)
        ..files.add(await http.MultipartFile.fromPath(
          'driverLicenseFile', 
          licenseFile.path
        ));
      
      var response = await request.send();

      if (response.statusCode == 200) {
        return ProfileServiceResult(
          success: true,
          message: "Saved Profile Success",
        );
      } else {
        return ProfileServiceResult(
          success: false,
          message: "Internal Server Error - ${response.statusCode}",
        );
      }
    } catch (e) {
      return ProfileServiceResult(
        success: false,
        message: "Please check your network connection",
      );
    }
  }

  /// Validates profile form data
  static ProfileValidationResult validateProfileData({
    required String fullName,
    required String address,
    required String citizenId,
    required File? licenseFile,
  }) {
    List<String> errors = [];

    if (fullName.trim().isEmpty) {
      errors.add("Full name is required");
    }

    if (address.trim().isEmpty) {
      errors.add("Address is required");
    }

    if (citizenId.trim().isEmpty) {
      errors.add("Citizen ID is required");
    }

    if (licenseFile == null) {
      errors.add("Driver license file is required");
    }

    return ProfileValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
    );
  }
}

/// Result class for profile service operations
class ProfileServiceResult {
  final bool success;
  final String message;
  final dynamic data;

  ProfileServiceResult({
    required this.success,
    required this.message,
    this.data,
  });
}

/// Result class for profile validation
class ProfileValidationResult {
  final bool isValid;
  final List<String> errors;

  ProfileValidationResult({
    required this.isValid,
    required this.errors,
  });
}
