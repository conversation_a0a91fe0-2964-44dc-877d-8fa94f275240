import 'dart:convert';
import 'package:aslaa/constant.dart';
import 'package:aslaa/models/device.dart';
import 'package:aslaa/providers/app_provider.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';

/// Service class responsible for device-related API operations
class DeviceService {
  /// Saves or updates device information
  static Future<DeviceServiceResult> saveDevice({
    required AppProvider authProvider,
    required String deviceName,
    required String deviceNumber,
    required String? uix,
    required bool isDefault,
    required String type,
    required String mode, // 'new' or 'edit'
    Device? selectedDevice,
  }) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String token = prefs.getString('token') ?? authProvider.user!.token;
      
      Map<String, String> headers = {
        'Content-Type': 'application/json; charset=UTF-8',
        'Authorization': 'Bearer $token',
      };
      
      Map<String, dynamic> body = {
        "deviceName": deviceName,
        "deviceNumber": deviceNumber,
        "uix": uix,
        "isDefault": isDefault,
        "type": type.toLowerCase(),
        "phoneNumber": authProvider.user?.phoneNumber,
        "_id": mode == 'edit' ? selectedDevice?.id : '',
      };
      
      var response = await http.post(
        Uri.parse('$API_HOST/api/device/register'),
        headers: headers,
        body: jsonEncode(body),
      );
      
      if (response.statusCode == 200) {
        Map<String, dynamic> json = jsonDecode(response.body);
        if (json['success']) {
          return DeviceServiceResult(
            success: true,
            message: "Saved Device Information Success",
          );
        } else {
          return DeviceServiceResult(
            success: false,
            message: json['message'] ?? 'Please check your device details',
          );
        }
      } else {
        return DeviceServiceResult(
          success: false,
          message: "Internal Server Error - ${response.statusCode}",
        );
      }
    } catch (err) {
      return DeviceServiceResult(
        success: false,
        message: "Please check your network connection",
      );
    }
  }

  /// Deletes a device
  static Future<DeviceServiceResult> deleteDevice({
    required AppProvider authProvider,
    required Device device,
  }) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String token = prefs.getString('token') ?? authProvider.user!.token;
      
      Map<String, String> headers = {
        'Content-Type': 'application/json; charset=UTF-8',
        'Authorization': 'Bearer $token',
      };

      var response = await http.delete(
        Uri.parse('$API_HOST/api/device/delete/${device.id}'),
        headers: headers,
      );
      
      if (response.statusCode == 200) {
        Map<String, dynamic> json = jsonDecode(response.body);
        if (json['success']) {
          return DeviceServiceResult(
            success: true,
            message: "Deleted Device",
          );
        } else {
          return DeviceServiceResult(
            success: false,
            message: json['message'] ?? 'Please check your device details',
          );
        }
      } else {
        return DeviceServiceResult(
          success: false,
          message: "Internal Server Error - ${response.statusCode}",
        );
      }
    } catch (err) {
      return DeviceServiceResult(
        success: false,
        message: "Please check your network connection",
      );
    }
  }

  /// Validates device form data
  static DeviceValidationResult validateDeviceData({
    required String deviceName,
    required String deviceNumber,
    required String? uix,
    required String type,
  }) {
    List<String> errors = [];

    if (deviceName.trim().isEmpty) {
      errors.add("Device name is required");
    }

    if (deviceNumber.trim().isEmpty) {
      errors.add("Device number is required");
    }

    if (uix == null || uix.trim().isEmpty) {
      errors.add("UIX selection is required");
    }

    if (type.trim().isEmpty) {
      errors.add("Device type is required");
    }

    return DeviceValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
    );
  }
}

/// Result class for device service operations
class DeviceServiceResult {
  final bool success;
  final String message;
  final dynamic data;

  DeviceServiceResult({
    required this.success,
    required this.message,
    this.data,
  });
}

/// Result class for device validation
class DeviceValidationResult {
  final bool isValid;
  final List<String> errors;

  DeviceValidationResult({
    required this.isValid,
    required this.errors,
  });
}
