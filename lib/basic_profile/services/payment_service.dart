import 'dart:convert';
import 'dart:typed_data';
import 'package:aslaa/constant.dart';
import 'package:aslaa/providers/app_provider.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';

/// Service class responsible for payment-related API operations
class PaymentService {
  /// Processes deposit request
  static Future<PaymentServiceResult> processDepositRequest({
    required AppProvider provider,
    required num amount,
  }) async {
    if (amount <= 1000) {
      return PaymentServiceResult(
        success: false,
        message: "Deposit amount must be greater than 1000",
      );
    }

    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String token = prefs.getString('token') ?? provider.user!.token;
      
      Map<String, String> headers = {
        'Content-Type': 'application/json; charset=UTF-8',
        'Authorization': 'Bearer $token',
      };
      
      Map<String, dynamic> body = {
        "totalCost": amount,
        "page": 'balance',
      };
      
      var response = await http.post(
        Uri.parse('$API_HOST/api/device/extend-balance'),
        headers: headers,
        body: jsonEncode(body),
      );
      
      if (response.statusCode == 200) {
        Map<String, dynamic> json = jsonDecode(response.body);
        if (json['data'] != null) {
          Map<String, dynamic> invoice = json['data']["bankList"];
          List<dynamic>? urls = invoice['urls'];
          String qrcode = invoice['qr_image'];
          
          return PaymentServiceResult(
            success: true,
            message: "Deposit request processed successfully",
            data: {
              'qrcode': qrcode,
              'urls': urls,
            },
          );
        } else {
          return PaymentServiceResult(
            success: false,
            message: "Invalid response from server",
          );
        }
      } else {
        return PaymentServiceResult(
          success: false,
          message: "Internal Server Error - ${response.statusCode}",
        );
      }
    } catch (err) {
      return PaymentServiceResult(
        success: false,
        message: "Please check your network connection",
      );
    }
  }

  /// Processes withdrawal request
  static Future<PaymentServiceResult> processWithdrawRequest({
    required AppProvider provider,
    required num amount,
  }) async {
    if (amount <= 1000) {
      return PaymentServiceResult(
        success: false,
        message: "Withdraw amount cannot be less than 1000",
      );
    }

    if (amount > (provider.user?.balance ?? 0)) {
      return PaymentServiceResult(
        success: false,
        message: "Withdraw amount cannot be greater than user balance",
      );
    }

    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String token = prefs.getString('token') ?? provider.user!.token;
      
      Map<String, String> headers = {
        'Content-Type': 'application/json; charset=UTF-8',
        'Authorization': 'Bearer $token',
      };
      
      Map<String, dynamic> body = {
        "payAmount": amount,
      };
      
      var response = await http.post(
        Uri.parse('$API_HOST/api/auth/request-withdraw'),
        headers: headers,
        body: jsonEncode(body),
      );
      
      if (response.statusCode == 200) {
        Map<String, dynamic> json = jsonDecode(response.body);
        if (json['success']) {
          return PaymentServiceResult(
            success: true,
            message: "Submitted your withdraw request",
          );
        } else {
          return PaymentServiceResult(
            success: false,
            message: json['message'] ?? 'Whoops! please try again',
          );
        }
      } else {
        return PaymentServiceResult(
          success: false,
          message: "Internal Server Error - ${response.statusCode}",
        );
      }
    } catch (err) {
      return PaymentServiceResult(
        success: false,
        message: "Please check your network connection",
      );
    }
  }

  /// Converts base64 image string to Uint8List
  static Uint8List convertBase64Image(String base64String) {
    return Base64Decoder().convert(base64String.split(',').last);
  }

  /// Validates payment amount
  static PaymentValidationResult validatePaymentAmount({
    required num? amount,
    required num? userBalance,
    required bool isWithdraw,
  }) {
    List<String> errors = [];

    if (amount == null || amount <= 0) {
      errors.add("Please enter a valid amount");
    } else if (amount <= 1000) {
      errors.add("Amount must be greater than 1000");
    } else if (isWithdraw && userBalance != null && amount > userBalance) {
      errors.add("Amount cannot be greater than your balance");
    }

    return PaymentValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
    );
  }
}

/// Result class for payment service operations
class PaymentServiceResult {
  final bool success;
  final String message;
  final dynamic data;

  PaymentServiceResult({
    required this.success,
    required this.message,
    this.data,
  });
}

/// Result class for payment validation
class PaymentValidationResult {
  final bool isValid;
  final List<String> errors;

  PaymentValidationResult({
    required this.isValid,
    required this.errors,
  });
}
