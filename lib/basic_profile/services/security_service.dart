import 'dart:convert';
import 'package:aslaa/constant.dart';
import 'package:aslaa/models/user.dart';
import 'package:aslaa/providers/app_provider.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';

/// Service class responsible for security-related API operations
class SecurityService {
  /// Updates user password/pin code
  static Future<SecurityServiceResult> updatePassword({
    required AppProvider authProvider,
    required String oldPassword,
    required String newPassword,
    required String confirmPassword,
  }) async {
    try {
      User user = authProvider.user!;
      
      // Validate passwords match
      if (confirmPassword != newPassword) {
        return SecurityServiceResult(
          success: false,
          message: "Please check your password and confirm password",
        );
      }
      
      // Validate old password
      if (oldPassword != user.password) {
        return SecurityServiceResult(
          success: false,
          message: "Your previous password is not matched",
        );
      }

      SharedPreferences prefs = await SharedPreferences.getInstance();
      String token = prefs.getString('token') ?? user.token;
      
      Map<String, String> headers = {
        'Content-Type': 'application/json; charset=UTF-8',
        'Authorization': 'Bearer $token',
      };
      
      Map<String, String> body = {
        "oldPinCode": oldPassword,
        "newPinCode": newPassword,
        "phoneNumber": user.phoneNumber,
        "username": user.username,
      };

      var response = await http.post(
        Uri.parse('$API_HOST/api/auth/set-pincode'),
        headers: headers,
        body: jsonEncode(body),
      );
      
      if (response.statusCode == 200) {
        return SecurityServiceResult(
          success: true,
          message: "Saved Security Success",
          data: {'newPassword': newPassword},
        );
      } else {
        return SecurityServiceResult(
          success: false,
          message: "Internal Server Error - ${response.statusCode}",
        );
      }
    } catch (err) {
      return SecurityServiceResult(
        success: false,
        message: "Please check your network connection",
      );
    }
  }

  /// Validates security form data
  static SecurityValidationResult validateSecurityData({
    required String oldPassword,
    required String newPassword,
    required String confirmPassword,
    required String currentUserPassword,
  }) {
    List<String> errors = [];

    if (oldPassword.trim().isEmpty) {
      errors.add("Current password is required");
    }

    if (newPassword.trim().isEmpty) {
      errors.add("New password is required");
    }

    if (confirmPassword.trim().isEmpty) {
      errors.add("Confirm password is required");
    }

    if (oldPassword != currentUserPassword) {
      errors.add("Current password is incorrect");
    }

    if (newPassword != confirmPassword) {
      errors.add("New password and confirm password do not match");
    }

    if (newPassword.length < 4) {
      errors.add("Password must be at least 4 characters long");
    }

    return SecurityValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
    );
  }
}

/// Result class for security service operations
class SecurityServiceResult {
  final bool success;
  final String message;
  final dynamic data;

  SecurityServiceResult({
    required this.success,
    required this.message,
    this.data,
  });
}

/// Result class for security validation
class SecurityValidationResult {
  final bool isValid;
  final List<String> errors;

  SecurityValidationResult({
    required this.isValid,
    required this.errors,
  });
}
