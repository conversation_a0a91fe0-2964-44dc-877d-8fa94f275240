import 'package:flutter/material.dart';
import 'package:awesome_snackbar_content/awesome_snackbar_content.dart';
import '../../flutter_flow/flutter_flow_util.dart';

/// Utility class for common UI operations
class UIUtils {
  /// Shows a success snackbar
  static void showSuccessSnackbar(BuildContext context, String message, {String title = "Success"}) {
    showAnimatedSnackbar(context, message, title, ContentType.success);
  }

  /// Shows an error snackbar
  static void showErrorSnackbar(BuildContext context, String message, {String title = "Error"}) {
    showAnimatedSnackbar(context, message, title, ContentType.failure);
  }

  /// Shows a warning snackbar
  static void showWarningSnackbar(BuildContext context, String message, {String title = "Warning"}) {
    showAnimatedSnackbar(context, message, title, ContentType.warning);
  }

  /// Shows an info snackbar
  static void showInfoSnackbar(BuildContext context, String message, {String title = "Info"}) {
    showAnimatedSnackbar(context, message, title, ContentType.help);
  }

  /// Shows a confirmation dialog
  static Future<bool> showConfirmationDialog({
    required BuildContext context,
    required String title,
    required String message,
    String confirmText = "Confirm",
    String cancelText = "Cancel",
    Color? confirmColor,
  }) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(title),
          content: Text(message),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: Text(cancelText),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: confirmColor != null
                  ? TextButton.styleFrom(foregroundColor: confirmColor)
                  : null,
              child: Text(confirmText),
            ),
          ],
        );
      },
    );
    return result ?? false;
  }

  /// Shows a loading dialog
  static void showLoadingDialog(BuildContext context, {String message = "Loading..."}) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text(message),
            ],
          ),
        );
      },
    );
  }

  /// Hides the loading dialog
  static void hideLoadingDialog(BuildContext context) {
    Navigator.of(context).pop();
  }

  /// Shows a bottom sheet with options
  static Future<T?> showOptionsBottomSheet<T>({
    required BuildContext context,
    required String title,
    required List<BottomSheetOption<T>> options,
  }) async {
    return await showModalBottomSheet<T>(
      context: context,
      builder: (BuildContext context) {
        return Container(
          padding: EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: Theme.of(context).textTheme.titleLarge,
              ),
              SizedBox(height: 16),
              ...options.map((option) => ListTile(
                leading: option.icon,
                title: Text(option.title),
                subtitle: option.subtitle != null ? Text(option.subtitle!) : null,
                onTap: () => Navigator.of(context).pop(option.value),
              )).toList(),
            ],
          ),
        );
      },
    );
  }

  /// Unfocuses any focused text field
  static void unfocusAll(BuildContext context) {
    FocusScope.of(context).unfocus();
  }

  /// Formats a number with thousand separators
  static String formatNumber(num? value) {
    if (value == null) return '';
    return getFormatedNumber(value);
  }

  /// Gets a safe area padding for the bottom
  static double getBottomSafeAreaPadding(BuildContext context) {
    return MediaQuery.of(context).padding.bottom;
  }

  /// Gets a safe area padding for the top
  static double getTopSafeAreaPadding(BuildContext context) {
    return MediaQuery.of(context).padding.top;
  }

  /// Checks if the device is in dark mode
  static bool isDarkMode(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark;
  }

  /// Gets the screen width
  static double getScreenWidth(BuildContext context) {
    return MediaQuery.of(context).size.width;
  }

  /// Gets the screen height
  static double getScreenHeight(BuildContext context) {
    return MediaQuery.of(context).size.height;
  }

  /// Checks if the device is a tablet (width > 600)
  static bool isTablet(BuildContext context) {
    return getScreenWidth(context) > 600;
  }

  /// Shows a simple alert dialog
  static Future<void> showAlertDialog({
    required BuildContext context,
    required String title,
    required String message,
    String buttonText = "OK",
  }) async {
    await showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(title),
          content: Text(message),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(buttonText),
            ),
          ],
        );
      },
    );
  }
}

/// Class representing a bottom sheet option
class BottomSheetOption<T> {
  final String title;
  final String? subtitle;
  final Icon? icon;
  final T value;

  BottomSheetOption({
    required this.title,
    this.subtitle,
    this.icon,
    required this.value,
  });
}
