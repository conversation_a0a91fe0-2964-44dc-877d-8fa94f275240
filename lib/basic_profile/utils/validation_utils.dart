/// Utility class for common validation operations
class ValidationUtils {
  /// Validates if a string is not empty after trimming
  static bool isNotEmpty(String? value) {
    return value != null && value.trim().isNotEmpty;
  }

  /// Validates if a string has minimum length
  static bool hasMinLength(String? value, int minLength) {
    return value != null && value.trim().length >= minLength;
  }

  /// Validates if a string has maximum length
  static bool hasMaxLength(String? value, int maxLength) {
    return value != null && value.trim().length <= maxLength;
  }

  /// Validates if a string is a valid email format
  static bool isValidEmail(String? value) {
    if (value == null || value.trim().isEmpty) return false;
    
    final emailRegex = RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    );
    return emailRegex.hasMatch(value.trim());
  }

  /// Validates if a string is a valid phone number (basic validation)
  static bool isValidPhoneNumber(String? value) {
    if (value == null || value.trim().isEmpty) return false;
    
    final phoneRegex = RegExp(r'^\+?[1-9]\d{1,14}$');
    return phoneRegex.hasMatch(value.trim().replaceAll(RegExp(r'[\s-()]'), ''));
  }

  /// Validates if a string contains only numbers
  static bool isNumeric(String? value) {
    if (value == null || value.trim().isEmpty) return false;
    return num.tryParse(value.trim()) != null;
  }

  /// Validates if a number is within a range
  static bool isInRange(num? value, num min, num max) {
    return value != null && value >= min && value <= max;
  }

  /// Validates if two strings match
  static bool doStringsMatch(String? value1, String? value2) {
    return value1 != null && value2 != null && value1 == value2;
  }

  /// Validates bank account number format
  static bool isValidBankAccount(String? value) {
    if (value == null || value.trim().isEmpty) return false;
    
    final cleanValue = value.trim().replaceAll(RegExp(r'[\s-]'), '');
    return cleanValue.length >= 8 && cleanValue.length <= 20 && isNumeric(cleanValue);
  }

  /// Validates device number format
  static bool isValidDeviceNumber(String? value) {
    if (value == null || value.trim().isEmpty) return false;
    
    final cleanValue = value.trim();
    return cleanValue.length >= 6 && cleanValue.length <= 20;
  }

  /// Validates password strength
  static PasswordStrength getPasswordStrength(String? password) {
    if (password == null || password.isEmpty) {
      return PasswordStrength.empty;
    }

    if (password.length < 4) {
      return PasswordStrength.weak;
    }

    if (password.length < 6) {
      return PasswordStrength.fair;
    }

    bool hasUppercase = password.contains(RegExp(r'[A-Z]'));
    bool hasLowercase = password.contains(RegExp(r'[a-z]'));
    bool hasNumbers = password.contains(RegExp(r'[0-9]'));
    bool hasSpecialCharacters = password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'));

    int strengthScore = 0;
    if (hasUppercase) strengthScore++;
    if (hasLowercase) strengthScore++;
    if (hasNumbers) strengthScore++;
    if (hasSpecialCharacters) strengthScore++;

    if (password.length >= 8 && strengthScore >= 3) {
      return PasswordStrength.strong;
    } else if (password.length >= 6 && strengthScore >= 2) {
      return PasswordStrength.good;
    } else {
      return PasswordStrength.fair;
    }
  }

  /// Gets validation error message for required field
  static String getRequiredFieldError(String fieldName) {
    return '$fieldName is required';
  }

  /// Gets validation error message for minimum length
  static String getMinLengthError(String fieldName, int minLength) {
    return '$fieldName must be at least $minLength characters long';
  }

  /// Gets validation error message for maximum length
  static String getMaxLengthError(String fieldName, int maxLength) {
    return '$fieldName must be no more than $maxLength characters long';
  }

  /// Gets validation error message for invalid format
  static String getInvalidFormatError(String fieldName) {
    return '$fieldName has an invalid format';
  }

  /// Gets validation error message for passwords not matching
  static String getPasswordMismatchError() {
    return 'Passwords do not match';
  }

  /// Gets validation error message for invalid range
  static String getRangeError(String fieldName, num min, num max) {
    return '$fieldName must be between $min and $max';
  }
}

/// Enum for password strength levels
enum PasswordStrength {
  empty,
  weak,
  fair,
  good,
  strong,
}

/// Extension to get password strength description
extension PasswordStrengthExtension on PasswordStrength {
  String get description {
    switch (this) {
      case PasswordStrength.empty:
        return 'Enter a password';
      case PasswordStrength.weak:
        return 'Weak';
      case PasswordStrength.fair:
        return 'Fair';
      case PasswordStrength.good:
        return 'Good';
      case PasswordStrength.strong:
        return 'Strong';
    }
  }

  /// Gets the color associated with the password strength
  int get colorValue {
    switch (this) {
      case PasswordStrength.empty:
        return 0xFF9E9E9E; // Grey
      case PasswordStrength.weak:
        return 0xFFF44336; // Red
      case PasswordStrength.fair:
        return 0xFFFF9800; // Orange
      case PasswordStrength.good:
        return 0xFF2196F3; // Blue
      case PasswordStrength.strong:
        return 0xFF4CAF50; // Green
    }
  }
}
