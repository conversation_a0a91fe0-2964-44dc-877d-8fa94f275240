import 'dart:io';
import 'package:image_picker/image_picker.dart';

/// Utility class for image-related operations
class ImageUtils {
  static final ImagePicker _picker = ImagePicker();

  /// Picks an image from the specified source
  static Future<File?> pickImage({required ImageSource imageSource}) async {
    try {
      XFile? pickedFile = await _picker.pickImage(source: imageSource);
      if (pickedFile != null) {
        return File(pickedFile.path);
      }
      return null;
    } catch (e) {
      print('Error picking image: $e');
      return null;
    }
  }

  /// Picks an image from gallery
  static Future<File?> pickImageFromGallery() async {
    return await pickImage(imageSource: ImageSource.gallery);
  }

  /// Picks an image from camera
  static Future<File?> pickImageFromCamera() async {
    return await pickImage(imageSource: ImageSource.camera);
  }

  /// Validates if the file is a valid image
  static bool isValidImageFile(File? file) {
    if (file == null) return false;
    
    final String extension = file.path.toLowerCase();
    return extension.endsWith('.jpg') ||
           extension.endsWith('.jpeg') ||
           extension.endsWith('.png') ||
           extension.endsWith('.gif') ||
           extension.endsWith('.bmp');
  }

  /// Gets the file size in MB
  static double getFileSizeInMB(File file) {
    int sizeInBytes = file.lengthSync();
    return sizeInBytes / (1024 * 1024);
  }

  /// Validates image file size (max 10MB)
  static bool isValidFileSize(File file, {double maxSizeMB = 10.0}) {
    return getFileSizeInMB(file) <= maxSizeMB;
  }
}
