import 'package:flutter/material.dart';

/// Utility class for form-related operations
class FormUtils {
  /// Creates a list of TextEditingController instances
  static List<TextEditingController> createControllers(int count) {
    return List.generate(count, (index) => TextEditingController());
  }

  /// Disposes a list of TextEditingController instances
  static void disposeControllers(List<TextEditingController> controllers) {
    for (final controller in controllers) {
      controller.dispose();
    }
  }

  /// Clears all text in a list of controllers
  static void clearControllers(List<TextEditingController> controllers) {
    for (final controller in controllers) {
      controller.clear();
    }
  }

  /// Validates a form and returns the first error message if any
  static String? validateForm(GlobalKey<FormState> formKey) {
    if (!formKey.currentState!.validate()) {
      return "Please fix the errors in the form";
    }
    return null;
  }

  /// Saves form state
  static void saveForm(GlobalKey<FormState> formKey) {
    formKey.currentState?.save();
  }

  /// Resets form state
  static void resetForm(GlobalKey<FormState> formKey) {
    formKey.currentState?.reset();
  }

  /// Creates a validator for required fields
  static String? Function(String?) requiredValidator(String fieldName) {
    return (value) {
      if (value == null || value.trim().isEmpty) {
        return '$fieldName is required';
      }
      return null;
    };
  }

  /// Creates a validator for minimum length
  static String? Function(String?) minLengthValidator(String fieldName, int minLength) {
    return (value) {
      if (value == null || value.trim().length < minLength) {
        return '$fieldName must be at least $minLength characters';
      }
      return null;
    };
  }

  /// Creates a validator for maximum length
  static String? Function(String?) maxLengthValidator(String fieldName, int maxLength) {
    return (value) {
      if (value != null && value.trim().length > maxLength) {
        return '$fieldName must be no more than $maxLength characters';
      }
      return null;
    };
  }

  /// Creates a validator for email format
  static String? Function(String?) emailValidator() {
    return (value) {
      if (value == null || value.trim().isEmpty) {
        return 'Email is required';
      }
      
      final emailRegex = RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$');
      if (!emailRegex.hasMatch(value.trim())) {
        return 'Please enter a valid email address';
      }
      return null;
    };
  }

  /// Creates a validator for numeric fields
  static String? Function(String?) numericValidator(String fieldName, {num? min, num? max}) {
    return (value) {
      if (value == null || value.trim().isEmpty) {
        return '$fieldName is required';
      }
      
      final numValue = num.tryParse(value.trim());
      if (numValue == null) {
        return '$fieldName must be a valid number';
      }
      
      if (min != null && numValue < min) {
        return '$fieldName must be at least $min';
      }
      
      if (max != null && numValue > max) {
        return '$fieldName must be no more than $max';
      }
      
      return null;
    };
  }

  /// Creates a validator for password confirmation
  static String? Function(String?) confirmPasswordValidator(TextEditingController passwordController) {
    return (value) {
      if (value == null || value.trim().isEmpty) {
        return 'Confirm password is required';
      }
      
      if (value != passwordController.text) {
        return 'Passwords do not match';
      }
      
      return null;
    };
  }

  /// Creates a combined validator from multiple validators
  static String? Function(String?) combineValidators(List<String? Function(String?)> validators) {
    return (value) {
      for (final validator in validators) {
        final result = validator(value);
        if (result != null) {
          return result;
        }
      }
      return null;
    };
  }

  /// Formats text input for phone numbers
  static String formatPhoneNumber(String input) {
    // Remove all non-digit characters
    String digitsOnly = input.replaceAll(RegExp(r'[^\d]'), '');
    
    // Format based on length
    if (digitsOnly.length <= 3) {
      return digitsOnly;
    } else if (digitsOnly.length <= 6) {
      return '${digitsOnly.substring(0, 3)}-${digitsOnly.substring(3)}';
    } else if (digitsOnly.length <= 10) {
      return '${digitsOnly.substring(0, 3)}-${digitsOnly.substring(3, 6)}-${digitsOnly.substring(6)}';
    } else {
      return '${digitsOnly.substring(0, 3)}-${digitsOnly.substring(3, 6)}-${digitsOnly.substring(6, 10)}';
    }
  }

  /// Formats text input for bank account numbers
  static String formatBankAccount(String input) {
    // Remove all non-digit characters
    String digitsOnly = input.replaceAll(RegExp(r'[^\d]'), '');
    
    // Add spaces every 4 digits
    String formatted = '';
    for (int i = 0; i < digitsOnly.length; i++) {
      if (i > 0 && i % 4 == 0) {
        formatted += ' ';
      }
      formatted += digitsOnly[i];
    }
    
    return formatted;
  }

  /// Checks if any controller in a list has text
  static bool hasAnyText(List<TextEditingController> controllers) {
    return controllers.any((controller) => controller.text.trim().isNotEmpty);
  }

  /// Gets the total character count from all controllers
  static int getTotalCharacterCount(List<TextEditingController> controllers) {
    return controllers.fold(0, (sum, controller) => sum + controller.text.length);
  }

  /// Focuses the next field in a list of focus nodes
  static void focusNext(List<FocusNode> focusNodes, int currentIndex) {
    if (currentIndex < focusNodes.length - 1) {
      focusNodes[currentIndex + 1].requestFocus();
    }
  }

  /// Focuses the previous field in a list of focus nodes
  static void focusPrevious(List<FocusNode> focusNodes, int currentIndex) {
    if (currentIndex > 0) {
      focusNodes[currentIndex - 1].requestFocus();
    }
  }
}
