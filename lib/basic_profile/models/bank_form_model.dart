import 'package:flutter/material.dart';

/// Model class for managing bank form state
class BankFormModel {
  final TextEditingController bankAccountController;
  final TextEditingController bankNameController;
  
  bool isLoading;
  String? errorMessage;

  BankFormModel({
    TextEditingController? bankAccountController,
    TextEditingController? bankNameController,
    this.isLoading = false,
    this.errorMessage,
  }) : 
    bankAccountController = bankAccountController ?? TextEditingController(),
    bankNameController = bankNameController ?? TextEditingController();

  /// Creates a copy of the model with updated values
  BankFormModel copyWith({
    TextEditingController? bankAccountController,
    TextEditingController? bankNameController,
    bool? isLoading,
    String? errorMessage,
    bool clearError = false,
  }) {
    return BankFormModel(
      bankAccountController: bankAccountController ?? this.bankAccountController,
      bankNameController: bankNameController ?? this.bankNameController,
      isLoading: isLoading ?? this.isLoading,
      errorMessage: clearError ? null : (errorMessage ?? this.errorMessage),
    );
  }

  /// Validates the form data
  bool get isValid {
    return bankAccountController.text.trim().isNotEmpty &&
           bankNameController.text.trim().isNotEmpty &&
           bankAccountController.text.trim().length >= 8;
  }

  /// Gets validation errors
  List<String> get validationErrors {
    List<String> errors = [];
    
    if (bankAccountController.text.trim().isEmpty) {
      errors.add("Bank account number is required");
    } else if (bankAccountController.text.trim().length < 8) {
      errors.add("Bank account number must be at least 8 characters");
    }
    
    if (bankNameController.text.trim().isEmpty) {
      errors.add("Bank name is required");
    }
    
    return errors;
  }

  /// Clears all form data
  void clear() {
    bankAccountController.clear();
    bankNameController.clear();
    errorMessage = null;
  }

  /// Disposes of controllers
  void dispose() {
    bankAccountController.dispose();
    bankNameController.dispose();
  }

  /// Populates form with bank data
  void populateFromBankData({
    required String bankAccount,
    required String bankName,
  }) {
    bankAccountController.text = bankAccount;
    bankNameController.text = bankName;
  }
}
