import 'package:flutter/material.dart';
import 'package:aslaa/models/device.dart';

/// Model class for managing device form state
class DeviceFormModel {
  final TextEditingController deviceNameController;
  final TextEditingController deviceNumberController;
  
  String? dropDownValue;
  String radioButtonValue;
  bool switchValue;
  String mode; // 'new' or 'edit'
  Device? selectedDevice;
  bool isLoading;
  String? errorMessage;

  DeviceFormModel({
    TextEditingController? deviceNameController,
    TextEditingController? deviceNumberController,
    this.dropDownValue,
    this.radioButtonValue = '4G',
    this.switchValue = false,
    this.mode = 'new',
    this.selectedDevice,
    this.isLoading = false,
    this.errorMessage,
  }) : 
    deviceNameController = deviceNameController ?? TextEditingController(),
    deviceNumberController = deviceNumberController ?? TextEditingController();

  /// Creates a copy of the model with updated values
  DeviceFormModel copyWith({
    TextEditingController? deviceNameController,
    TextEditingController? deviceNumberController,
    String? dropDownValue,
    String? radioButtonValue,
    bool? switchValue,
    String? mode,
    Device? selectedDevice,
    bool? isLoading,
    String? errorMessage,
    bool clearError = false,
    bool clearDevice = false,
  }) {
    return DeviceFormModel(
      deviceNameController: deviceNameController ?? this.deviceNameController,
      deviceNumberController: deviceNumberController ?? this.deviceNumberController,
      dropDownValue: dropDownValue ?? this.dropDownValue,
      radioButtonValue: radioButtonValue ?? this.radioButtonValue,
      switchValue: switchValue ?? this.switchValue,
      mode: mode ?? this.mode,
      selectedDevice: clearDevice ? null : (selectedDevice ?? this.selectedDevice),
      isLoading: isLoading ?? this.isLoading,
      errorMessage: clearError ? null : (errorMessage ?? this.errorMessage),
    );
  }

  /// Validates the form data
  bool get isValid {
    return deviceNameController.text.trim().isNotEmpty &&
           deviceNumberController.text.trim().isNotEmpty &&
           dropDownValue != null &&
           dropDownValue!.trim().isNotEmpty;
  }

  /// Gets validation errors
  List<String> get validationErrors {
    List<String> errors = [];
    
    if (deviceNameController.text.trim().isEmpty) {
      errors.add("Device name is required");
    }
    
    if (deviceNumberController.text.trim().isEmpty) {
      errors.add("Device number is required");
    }
    
    if (dropDownValue == null || dropDownValue!.trim().isEmpty) {
      errors.add("UIX selection is required");
    }
    
    return errors;
  }

  /// Clears all form data
  void clear() {
    deviceNameController.clear();
    deviceNumberController.clear();
    dropDownValue = null;
    radioButtonValue = '4G';
    switchValue = false;
    mode = 'new';
    selectedDevice = null;
    errorMessage = null;
  }

  /// Disposes of controllers
  void dispose() {
    deviceNameController.dispose();
    deviceNumberController.dispose();
  }

  /// Populates form with device data for editing
  void populateFromDevice(Device device) {
    mode = 'edit';
    selectedDevice = device;
    deviceNameController.text = device.deviceName;
    deviceNumberController.text = device.deviceNumber;
    dropDownValue = device.uix;
    radioButtonValue = device.type.toUpperCase();
    switchValue = device.isDefault;
  }

  /// Resets form for new device creation
  void resetForNewDevice() {
    clear();
    mode = 'new';
  }
}
