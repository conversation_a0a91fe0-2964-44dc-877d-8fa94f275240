import 'package:flutter/material.dart';

/// Model class for managing payment form state (deposit/withdraw)
class PaymentFormModel {
  final TextEditingController amountController;
  
  bool isLoading;
  String? errorMessage;
  PaymentType paymentType;

  PaymentFormModel({
    TextEditingController? amountController,
    this.isLoading = false,
    this.errorMessage,
    this.paymentType = PaymentType.deposit,
  }) : amountController = amountController ?? TextEditingController();

  /// Creates a copy of the model with updated values
  PaymentFormModel copyWith({
    TextEditingController? amountController,
    bool? isLoading,
    String? errorMessage,
    PaymentType? paymentType,
    bool clearError = false,
  }) {
    return PaymentFormModel(
      amountController: amountController ?? this.amountController,
      isLoading: isLoading ?? this.isLoading,
      errorMessage: clearError ? null : (errorMessage ?? this.errorMessage),
      paymentType: paymentType ?? this.paymentType,
    );
  }

  /// Gets the amount as a number
  num? get amount {
    return num.tryParse(amountController.text);
  }

  /// Validates the form data
  bool isValidForBalance(num? userBalance) {
    final amt = amount;
    if (amt == null || amt <= 1000) return false;
    
    if (paymentType == PaymentType.withdraw && userBalance != null) {
      return amt <= userBalance;
    }
    
    return true;
  }

  /// Gets validation errors
  List<String> getValidationErrors(num? userBalance) {
    List<String> errors = [];
    final amt = amount;
    
    if (amountController.text.trim().isEmpty) {
      errors.add("Amount is required");
    } else if (amt == null) {
      errors.add("Please enter a valid amount");
    } else if (amt <= 1000) {
      errors.add("Amount must be greater than 1000");
    } else if (paymentType == PaymentType.withdraw && userBalance != null && amt > userBalance) {
      errors.add("Amount cannot be greater than your balance");
    }
    
    return errors;
  }

  /// Clears all form data
  void clear() {
    amountController.clear();
    errorMessage = null;
  }

  /// Disposes of controllers
  void dispose() {
    amountController.dispose();
  }

  /// Sets the payment type
  void setPaymentType(PaymentType type) {
    paymentType = type;
  }
}

/// Enum for payment types
enum PaymentType {
  deposit,
  withdraw,
}
