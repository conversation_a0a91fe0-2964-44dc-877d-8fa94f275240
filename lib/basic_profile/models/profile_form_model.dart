import 'dart:io';
import 'package:flutter/material.dart';

/// Model class for managing profile form state
class ProfileFormModel {
  final TextEditingController fullNameController;
  final TextEditingController citizenController;
  final TextEditingController addressController;
  
  File? selectedImage;
  bool isLoading;
  String? errorMessage;

  ProfileFormModel({
    TextEditingController? fullNameController,
    TextEditingController? citizenController,
    TextEditingController? addressController,
    this.selectedImage,
    this.isLoading = false,
    this.errorMessage,
  }) : 
    fullNameController = fullNameController ?? TextEditingController(),
    citizenController = citizenController ?? TextEditingController(),
    addressController = addressController ?? TextEditingController();

  /// Creates a copy of the model with updated values
  ProfileFormModel copyWith({
    TextEditingController? fullNameController,
    TextEditingController? citizenController,
    TextEditingController? addressController,
    File? selectedImage,
    bool? isLoading,
    String? errorMessage,
    bool clearImage = false,
    bool clearError = false,
  }) {
    return ProfileFormModel(
      fullNameController: fullNameController ?? this.fullNameController,
      citizenController: citizenController ?? this.citizenController,
      addressController: addressController ?? this.addressController,
      selectedImage: clearImage ? null : (selectedImage ?? this.selectedImage),
      isLoading: isLoading ?? this.isLoading,
      errorMessage: clearError ? null : (errorMessage ?? this.errorMessage),
    );
  }

  /// Validates the form data
  bool get isValid {
    return fullNameController.text.trim().isNotEmpty &&
           citizenController.text.trim().isNotEmpty &&
           addressController.text.trim().isNotEmpty;
  }

  /// Gets validation errors
  List<String> get validationErrors {
    List<String> errors = [];
    
    if (fullNameController.text.trim().isEmpty) {
      errors.add("Full name is required");
    }
    
    if (citizenController.text.trim().isEmpty) {
      errors.add("Citizen ID is required");
    }
    
    if (addressController.text.trim().isEmpty) {
      errors.add("Address is required");
    }
    
    return errors;
  }

  /// Clears all form data
  void clear() {
    fullNameController.clear();
    citizenController.clear();
    addressController.clear();
    selectedImage = null;
    errorMessage = null;
  }

  /// Disposes of controllers
  void dispose() {
    fullNameController.dispose();
    citizenController.dispose();
    addressController.dispose();
  }

  /// Populates form with user data
  void populateFromUser({
    required String fullName,
    required String citizenId,
    required String address,
  }) {
    fullNameController.text = fullName;
    citizenController.text = citizenId;
    addressController.text = address;
  }
}
