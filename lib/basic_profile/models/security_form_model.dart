import 'package:flutter/material.dart';

/// Model class for managing security form state
class SecurityFormModel {
  final TextEditingController oldPasswordController;
  final TextEditingController passwordController;
  final TextEditingController confirmPasswordController;
  
  bool oldPasswordVisibility;
  bool passwordVisibility;
  bool confirmPasswordVisibility;
  bool isLoading;
  String? errorMessage;

  SecurityFormModel({
    TextEditingController? oldPasswordController,
    TextEditingController? passwordController,
    TextEditingController? confirmPasswordController,
    this.oldPasswordVisibility = false,
    this.passwordVisibility = false,
    this.confirmPasswordVisibility = false,
    this.isLoading = false,
    this.errorMessage,
  }) : 
    oldPasswordController = oldPasswordController ?? TextEditingController(),
    passwordController = passwordController ?? TextEditingController(),
    confirmPasswordController = confirmPasswordController ?? TextEditingController();

  /// Creates a copy of the model with updated values
  SecurityFormModel copyWith({
    TextEditingController? oldPasswordController,
    TextEditingController? passwordController,
    TextEditingController? confirmPasswordController,
    bool? oldPasswordVisibility,
    bool? passwordVisibility,
    bool? confirmPasswordVisibility,
    bool? isLoading,
    String? errorMessage,
    bool clearError = false,
  }) {
    return SecurityFormModel(
      oldPasswordController: oldPasswordController ?? this.oldPasswordController,
      passwordController: passwordController ?? this.passwordController,
      confirmPasswordController: confirmPasswordController ?? this.confirmPasswordController,
      oldPasswordVisibility: oldPasswordVisibility ?? this.oldPasswordVisibility,
      passwordVisibility: passwordVisibility ?? this.passwordVisibility,
      confirmPasswordVisibility: confirmPasswordVisibility ?? this.confirmPasswordVisibility,
      isLoading: isLoading ?? this.isLoading,
      errorMessage: clearError ? null : (errorMessage ?? this.errorMessage),
    );
  }

  /// Validates the form data
  bool isValidWithCurrentPassword(String currentUserPassword) {
    return oldPasswordController.text.trim().isNotEmpty &&
           passwordController.text.trim().isNotEmpty &&
           confirmPasswordController.text.trim().isNotEmpty &&
           oldPasswordController.text == currentUserPassword &&
           passwordController.text == confirmPasswordController.text &&
           passwordController.text.length >= 4;
  }

  /// Gets validation errors
  List<String> getValidationErrors(String currentUserPassword) {
    List<String> errors = [];
    
    if (oldPasswordController.text.trim().isEmpty) {
      errors.add("Current password is required");
    } else if (oldPasswordController.text != currentUserPassword) {
      errors.add("Current password is incorrect");
    }
    
    if (passwordController.text.trim().isEmpty) {
      errors.add("New password is required");
    } else if (passwordController.text.length < 4) {
      errors.add("Password must be at least 4 characters long");
    }
    
    if (confirmPasswordController.text.trim().isEmpty) {
      errors.add("Confirm password is required");
    } else if (passwordController.text != confirmPasswordController.text) {
      errors.add("New password and confirm password do not match");
    }
    
    return errors;
  }

  /// Clears all form data
  void clear() {
    oldPasswordController.clear();
    passwordController.clear();
    confirmPasswordController.clear();
    oldPasswordVisibility = false;
    passwordVisibility = false;
    confirmPasswordVisibility = false;
    errorMessage = null;
  }

  /// Disposes of controllers
  void dispose() {
    oldPasswordController.dispose();
    passwordController.dispose();
    confirmPasswordController.dispose();
  }

  /// Toggles old password visibility
  void toggleOldPasswordVisibility() {
    oldPasswordVisibility = !oldPasswordVisibility;
  }

  /// Toggles new password visibility
  void togglePasswordVisibility() {
    passwordVisibility = !passwordVisibility;
  }

  /// Toggles confirm password visibility
  void toggleConfirmPasswordVisibility() {
    confirmPasswordVisibility = !confirmPasswordVisibility;
  }
}
